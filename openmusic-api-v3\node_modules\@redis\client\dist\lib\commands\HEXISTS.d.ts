import { <PERSON>Parser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Determines whether a field exists in a hash
     * @param parser - The Redis command parser
     * @param key - Key of the hash
     * @param field - Field to check
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, field: RedisArgument) => void;
    readonly transformReply: () => NumberReply<0 | 1>;
};
export default _default;
//# sourceMappingURL=HEXISTS.d.ts.map