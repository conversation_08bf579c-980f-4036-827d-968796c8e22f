import { CommandParser } from '../client/parser';
import { RedisArgument, NullReply, A<PERSON>yReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the LPOP command with count parameter
     *
     * @param parser - The command parser
     * @param key - The key of the list to pop from
     * @param count - The number of elements to pop
     * @see https://redis.io/commands/lpop/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, count: number) => void;
    readonly transformReply: () => NullReply | ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=LPOP_COUNT.d.ts.map