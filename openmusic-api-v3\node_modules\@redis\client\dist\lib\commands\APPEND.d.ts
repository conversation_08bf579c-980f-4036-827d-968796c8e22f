import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Appends a value to a string key
     * @param parser - The Redis command parser
     * @param key - The key to append to
     * @param value - The value to append
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, value: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=APPEND.d.ts.map