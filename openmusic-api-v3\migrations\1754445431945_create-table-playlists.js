/* eslint-disable camelcase */

exports.shorthands = undefined;

exports.up = pgm => {
    pgm.createTable('playlists', {
        id: { 
            type: 'VARCHAR(50)', 
            primaryKey: true 
        },
        name: { 
            type: 'TEXT', 
            notNull: true 
        },
        owner: { 
            type: 'VARCHAR(50)', 
            notNull: true 
        },
    });

    // foreign key to user.id
    pgm.addConstraint('playlists', 'fk_playlists.owner_users.id', {
    foreignKeys: {
        columns: 'owner',
        references: 'users(id)',
        onDelete: 'cascade',
    },
});
};

exports.down = pgm => {
    pgm.dropTable('playlists');
};
