declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Executes a Lua script server side using the script's SHA1 digest
     * @param parser - The Redis command parser
     * @param sha1 - SHA1 digest of the script
     * @param options - Script execution options including keys and arguments
     */
    readonly parseCommand: (this: void, parser: import("../..").CommandParser, script: import("../RESP/types").RedisArgument, options?: import("./EVAL").EvalOptions | undefined) => void;
    readonly transformReply: () => import("../RESP/types").ReplyUnion;
};
export default _default;
//# sourceMappingURL=EVALSHA.d.ts.map