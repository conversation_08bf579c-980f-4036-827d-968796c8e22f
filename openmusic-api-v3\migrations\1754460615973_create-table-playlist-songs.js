/* eslint-disable camelcase */

exports.shorthands = undefined;

exports.up = pgm => {
    pgm.createTable('playlist_songs', {
        id: {
        type: 'VARCHAR(50)',
        primaryKey: true,
        },
        playlist_id: {
        type: 'VARCHAR(50)',
        notNull: true,
        },
        song_id: {
        type: 'VARCHAR(50)',
        notNull: true,
        },
    });

    // foreign key to playlist.id
    pgm.addConstraint('playlist_songs', 'fk_playlist_songs.playlist_id', {
        foreignKeys: {
        columns: 'playlist_id',
        references: 'playlists(id)',
        onDelete: 'cascade',
        },
    });

    // foreign key to songs.id
    pgm.addConstraint('playlist_songs', 'fk_playlist_songs.song_id', {
        foreignKeys: {
        columns: 'song_id',
        references: 'songs(id)',
        onDelete: 'cascade',
        },
    });
};

exports.down = pgm => {
    pgm.dropTable('playlist_songs');
};
