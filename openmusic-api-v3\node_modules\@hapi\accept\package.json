{"name": "@hapi/accept", "description": "HTTP Accept-* headers parsing", "version": "6.0.3", "repository": "git://github.com/hapijs/accept", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["HTTP", "header", "accept", "accept-encoding"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}