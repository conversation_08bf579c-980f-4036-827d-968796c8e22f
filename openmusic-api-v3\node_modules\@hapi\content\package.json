{"name": "@hapi/content", "description": "HTTP Content-* headers parsing", "version": "6.0.0", "repository": "git://github.com/hapijs/content", "main": "lib/index.js", "files": ["lib"], "keywords": ["HTTP", "header", "content", "content-type", "content-disposition"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/boom": "^10.0.0"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.0.1"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}