import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the INCR command
     *
     * @param parser - The command parser
     * @param key - The key to increment
     * @see https://redis.io/commands/incr/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=INCR.d.ts.map