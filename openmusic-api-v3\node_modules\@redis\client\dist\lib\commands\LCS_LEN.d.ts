import { NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LCS command with LEN option
     *
     * @param args - The same parameters as LCS command
     * @see https://redis.io/commands/lcs/
     */
    readonly parseCommand: (this: void, parser: import("../..").CommandParser, key1: import("../RESP/types").RedisArgument, key2: import("../RESP/types").RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=LCS_LEN.d.ts.map