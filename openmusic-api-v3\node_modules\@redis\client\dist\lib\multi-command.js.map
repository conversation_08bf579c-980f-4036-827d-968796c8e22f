{"version": 3, "file": "multi-command.js", "sourceRoot": "", "sources": ["../../lib/multi-command.ts"], "names": [], "mappings": ";;AACA,qCAAuD;AAgBvD,MAAqB,iBAAiB;IACnB,WAAW,CAAe;IAE3C,YAAY,WAAyB;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAEQ,KAAK,GAAmC,EAAE,CAAC;IAE3C,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;IAE1C,UAAU,CAAC,IAAsB,EAAE,cAA+B;QAChE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACd,IAAI;YACJ,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAED,SAAS,CAAC,MAAmB,EAAE,IAAsB,EAAE,cAA+B;QACpF,MAAM,SAAS,GAAqB,EAAE,CAAC;QACvC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACnC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACxC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAExB,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAC7C,CAAC;IAED,gBAAgB,CAAC,UAA0B;QACzC,MAAM,YAAY,GAAkB,EAAE,EACpC,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,KAAK,YAAY,mBAAU,EAAE,CAAC;gBAChC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,OAAO,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACzF,CAAC,CAAC,CAAC;QAEL,IAAI,YAAY,CAAC,MAAM;YAAE,MAAM,IAAI,wBAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AApDD,oCAoDC"}