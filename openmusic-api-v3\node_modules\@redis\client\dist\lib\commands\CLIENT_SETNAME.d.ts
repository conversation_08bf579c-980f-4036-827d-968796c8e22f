import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Assigns a name to the current connection
     * @param parser - The Redis command parser
     * @param name - The name to assign to the connection
     */
    readonly parseCommand: (this: void, parser: CommandParser, name: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=CLIENT_SETNAME.d.ts.map