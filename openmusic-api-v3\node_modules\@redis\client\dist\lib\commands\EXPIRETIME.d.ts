import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Returns the absolute Unix timestamp (since January 1, 1970) at which the given key will expire
     * @param parser - The Redis command parser
     * @param key - Key to check expiration time
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=EXPIRETIME.d.ts.map