"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Returns the total number of commands available in the Redis server
     * @param parser - The Redis command parser
     */
    parseCommand(parser) {
        parser.push('COMMAND', 'COUNT');
    },
    transformReply: undefined
};
//# sourceMappingURL=COMMAND_COUNT.js.map