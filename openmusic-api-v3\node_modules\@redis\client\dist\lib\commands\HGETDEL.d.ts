import { CommandParser } from '../client/parser';
import { RedisVariadicArgument } from './generic-transformers';
import { RedisArgument, ArrayReply, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    /**
     * Gets and deletes the specified fields from a hash
     * @param parser - The Redis command parser
     * @param key - Key of the hash
     * @param fields - Fields to get and delete
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, fields: RedisVariadicArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply | NullReply>;
};
export default _default;
//# sourceMappingURL=HGETDEL.d.ts.map