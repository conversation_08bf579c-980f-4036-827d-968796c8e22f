import { CommandParser } from '../client/parser';
import { VerbatimStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns serialized information about the nodes in a Redis Cluster
     * @param parser - The Redis command parser
     */
    readonly parseCommand: (this: void, parser: CommandParser) => void;
    readonly transformReply: () => VerbatimStringReply;
};
export default _default;
//# sourceMappingURL=CLUSTER_NODES.d.ts.map