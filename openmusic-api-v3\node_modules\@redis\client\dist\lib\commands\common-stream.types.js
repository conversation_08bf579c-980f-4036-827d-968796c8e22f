"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.STREAM_DELETION_REPLY_CODES = exports.STREAM_DELETION_POLICY = void 0;
/** Common stream deletion policies
 *
 *  Added in Redis 8.2
 */
exports.STREAM_DELETION_POLICY = {
    /** Preserve references (default) */
    KEEPREF: "KEEPREF",
    /** Delete all references */
    DELREF: "DELREF",
    /** Only acknowledged entries */
    ACKED: "ACKED",
};
/** Common reply codes for stream deletion operations */
exports.STREAM_DELETION_REPLY_CODES = {
    /** ID not found */
    NOT_FOUND: -1,
    /** Entry deleted */
    DELETED: 1,
    /** Dangling references */
    DANGLING_REFS: 2,
};
//# sourceMappingURL=common-stream.types.js.map