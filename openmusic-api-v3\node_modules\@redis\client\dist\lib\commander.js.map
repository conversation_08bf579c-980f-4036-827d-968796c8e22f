{"version": 3, "file": "commander.js", "sourceRoot": "", "sources": ["../../lib/commander.ts"], "names": [], "mappings": ";;;AAiBA,wCAAwC;AACxC,SAAS,mCAAmC;IAC1C,MAAM,IAAI,KAAK,CAAC,kGAAkG,CAAC,CAAC;AACtH,CAAC;AAED,SAAgB,YAAY,CAK1B,EACA,SAAS,EACT,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,MAAM,EAC6B;IACnC,MAAM,IAAI,GAAG,MAAM,EAAE,IAAI,IAAI,CAAC,EAC5B,KAAK,GAAQ,KAAM,SAAQ,SAAS;KAAG,CAAC;IAE1C,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QACvD,IAAI,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACxE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,mCAAmC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,IAAI,MAAM,EAAE,OAAO,EAAE,CAAC;QACpB,KAAK,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAClE,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAChC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrD,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;oBACvE,GAAG,CAAC,IAAI,CAAC,GAAG,mCAAmC,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAED,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,IAAI,MAAM,EAAE,SAAS,EAAE,CAAC;QACtB,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACnE,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAChC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvD,GAAG,CAAC,IAAI,CAAC,GAAG,qBAAqB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD,CAAC;YAED,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,IAAI,MAAM,EAAE,OAAO,EAAE,CAAC;QACpB,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5D,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AA1DD,oCA0DC;AAED,SAAS,eAAe,CAAC,SAAc,EAAE,IAAiB,EAAE,GAAQ;IAClE,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,EAAE;QACrC,GAAG;YACD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;YACnB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,iBAAiB,CAAC,OAAgB,EAAE,IAAkB;IACpE,QAAQ,OAAO,OAAO,CAAC,cAAc,EAAE,CAAC;QACtC,KAAK,UAAU;YACb,OAAO,OAAO,CAAC,cAAc,CAAC;QAEhC,KAAK,QAAQ;YACX,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;AACH,CAAC;AARD,8CAQC;AAED,SAAgB,uBAAuB,CAAC,IAAY,EAAE,EAAiB;IACrE,MAAM,MAAM,GAAyB;QACnC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO;QACtC,IAAI;KACL,CAAC;IAEF,IAAI,EAAE,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;QACpC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAXD,0DAWC;AAED,SAAgB,qBAAqB,CAAC,MAAmB;IACvD,MAAM,MAAM,GAA2B;QACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;QAC9C,MAAM,CAAC,IAAI;KACZ,CAAC;IAEF,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;QACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAXD,sDAWC"}