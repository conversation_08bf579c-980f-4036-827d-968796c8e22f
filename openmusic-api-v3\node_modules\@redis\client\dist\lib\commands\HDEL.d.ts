import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Removes one or more fields from a hash
     * @param parser - The Redis command parser
     * @param key - Key of the hash
     * @param field - Field(s) to remove
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, field: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=HDEL.d.ts.map