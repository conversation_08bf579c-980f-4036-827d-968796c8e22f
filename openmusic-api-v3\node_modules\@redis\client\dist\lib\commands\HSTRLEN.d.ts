import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the HSTRLEN command
     *
     * @param parser - The command parser
     * @param key - The key of the hash
     * @param field - The field to get the string length of
     * @see https://redis.io/commands/hstrlen/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, field: RedisArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=HSTRLEN.d.ts.map