import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Deletes one or more users from the ACL
     * @param parser - The Redis command parser
     * @param username - Username(s) to delete
     */
    readonly parseCommand: (this: void, parser: CommandParser, username: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ACL_DELUSER.d.ts.map