// Definitions adapted from DefinitelyTyped, originally created by:
// <PERSON> <https://github.com/rafael<PERSON>uza<PERSON>>
// <PERSON> <https://github.com/jhsimms>
// <PERSON> <https://github.com/<PERSON>>
// <PERSON> <https://github.com/saboya>
// <PERSON> <https://github.com/lenovouser>

export * from './plugin';
export * from './response';
export * from './request';
export * from './route';
export * from './server';
export * from './utils';

// Kept for backwards compatibility only (remove in next major)

export namespace Utils {
    interface Dictionary<T> {
        [key: string]: T;
    }
}
