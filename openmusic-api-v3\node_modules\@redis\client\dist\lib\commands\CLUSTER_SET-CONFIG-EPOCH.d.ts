import { Command<PERSON>arser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Sets the configuration epoch for a Redis Cluster node
     * @param parser - The Redis command parser
     * @param configEpoch - The configuration epoch to set
     */
    readonly parseCommand: (this: void, parser: CommandParser, configEpoch: number) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=CLUSTER_SET-CONFIG-EPOCH.d.ts.map