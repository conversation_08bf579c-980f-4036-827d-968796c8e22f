import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Instructs the server about tracking or not keys in the next request
     * @param parser - The Redis command parser
     * @param value - Whether to enable (true) or disable (false) tracking
     */
    readonly parseCommand: (this: void, parser: CommandParser, value: boolean) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=CLIENT_CACHING.d.ts.map