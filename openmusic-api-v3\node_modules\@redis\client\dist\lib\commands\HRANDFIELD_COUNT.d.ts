import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the HRANDFIELD command with count parameter
     *
     * @param parser - The command parser
     * @param key - The key of the hash to get random fields from
     * @param count - The number of fields to return (positive: unique fields, negative: may repeat fields)
     * @see https://redis.io/commands/hrandfield/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, count: number) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=HRANDFIELD_COUNT.d.ts.map