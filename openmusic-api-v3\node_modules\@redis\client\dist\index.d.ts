export { RedisArgument, RedisFunctions, RedisModules, RedisScripts, RespVersions, TypeMapping, } from './lib/RESP/types';
export { RESP_TYPES } from './lib/RESP/decoder';
export { VerbatimString } from './lib/RESP/verbatim-string';
export { defineScript } from './lib/lua-script';
export * from './lib/errors';
import RedisClient, { RedisClientOptions, RedisClientType } from './lib/client';
export { RedisClientOptions, RedisClientType };
export declare const createClient: typeof RedisClient.create;
export { CommandParser } from './lib/client/parser';
import { RedisClientPool, RedisPoolOptions, RedisClientPoolType } from './lib/client/pool';
export { RedisClientPoolType, RedisPoolOptions };
export declare const createClientPool: typeof RedisClientPool.create;
import RedisCluster, { RedisClusterOptions, RedisClusterType } from './lib/cluster';
export { RedisClusterType, RedisClusterOptions };
export declare const createCluster: typeof RedisCluster.create;
import RedisSentinel from './lib/sentinel';
export { RedisSentinelOptions, RedisSentinelType } from './lib/sentinel/types';
export declare const createSentinel: typeof RedisSentinel.create;
export { GEO_REPLY_WITH, GeoReplyWith } from './lib/commands/GEOSEARCH_WITH';
export { SetOptions } from './lib/commands/SET';
export { REDIS_FLUSH_MODES } from './lib/commands/FLUSHALL';
export { BasicClientSideCache, BasicPooledClientSideCache } from './lib/client/cache';
//# sourceMappingURL=index.d.ts.map