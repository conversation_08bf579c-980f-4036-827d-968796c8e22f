import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the HRANDFIELD command
     *
     * @param parser - The command parser
     * @param key - The key of the hash to get a random field from
     * @see https://redis.io/commands/hrandfield/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=HRANDFIELD.d.ts.map